
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Plus, Edit, Eye, User } from 'lucide-react';
import { Link } from 'react-router-dom';

const Dashboard = () => {
  // Mock user properties data
  const userProperties = [
    {
      id: '1',
      title: 'Modern Downtown Apartment',
      type: 'rental',
      price: 2500,
      status: 'approved',
      createdAt: '2024-01-15',
      views: 45,
      leads: 8
    },
    {
      id: '2',
      title: 'Cozy Suburban House',
      type: 'buying',
      price: 450000,
      status: 'under_processing',
      createdAt: '2024-01-20',
      views: 12,
      leads: 3
    },
    {
      id: '3',
      title: 'Commercial Shop Space',
      type: 'shop',
      price: 3500,
      status: 'rejected',
      createdAt: '2024-01-10',
      views: 8,
      leads: 1,
      rejectionReason: 'Missing required documentation'
    }
  ];

  // Mock leads data
  const leads = [
    {
      id: '1',
      propertyTitle: 'Modern Downtown Apartment',
      userName: '<PERSON>',
      phone: '+****************',
      message: 'Very interested in viewing this property. When would be a good time?',
      createdAt: '2024-01-22'
    },
    {
      id: '2',
      propertyTitle: 'Modern Downtown Apartment',
      userName: 'Sarah Johnson',
      phone: '+****************',
      message: 'Is this still available? I can move in next month.',
      createdAt: '2024-01-21'
    },
    {
      id: '3',
      propertyTitle: 'Cozy Suburban House',
      userName: 'Mike Wilson',
      phone: '+****************',
      message: 'Looking for a family home. Would like to schedule a visit.',
      createdAt: '2024-01-20'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'under_processing':
        return 'bg-yellow-100 text-yellow-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'approved':
        return 'Approved';
      case 'under_processing':
        return 'Under Processing';
      case 'rejected':
        return 'Rejected';
      default:
        return status;
    }
  };

  const formatPrice = (price: number, type: string) => {
    if (type === 'rental' || type === 'shop') {
      return `$${price.toLocaleString()}/month`;
    }
    return `$${price.toLocaleString()}`;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
          <div>
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-2">
              Dashboard
            </h1>
            <p className="text-xl text-gray-600">
              Manage your properties and track leads
            </p>
          </div>
          <Link to="/list-property">
            <Button size="lg" className="mt-4 sm:mt-0">
              <Plus className="w-5 h-5 mr-2" />
              List New Property
            </Button>
          </Link>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="text-2xl font-bold text-primary">
                {userProperties.length}
              </div>
              <p className="text-gray-600">Total Properties</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="text-2xl font-bold text-green-600">
                {userProperties.filter(p => p.status === 'approved').length}
              </div>
              <p className="text-gray-600">Approved</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="text-2xl font-bold text-yellow-600">
                {userProperties.filter(p => p.status === 'under_processing').length}
              </div>
              <p className="text-gray-600">Under Review</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="text-2xl font-bold text-blue-600">
                {leads.length}
              </div>
              <p className="text-gray-600">Total Leads</p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="properties" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="properties">My Properties</TabsTrigger>
            <TabsTrigger value="leads">Leads</TabsTrigger>
          </TabsList>

          {/* Properties Tab */}
          <TabsContent value="properties" className="mt-6">
            <div className="space-y-6">
              {userProperties.map((property) => (
                <Card key={property.id}>
                  <CardContent className="p-6">
                    <div className="flex flex-col lg:flex-row justify-between items-start gap-4">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-xl font-semibold">{property.title}</h3>
                          <Badge className={getStatusColor(property.status)}>
                            {getStatusText(property.status)}
                          </Badge>
                        </div>
                        
                        <div className="flex flex-wrap gap-4 text-sm text-gray-600 mb-3">
                          <span>Type: {property.type.charAt(0).toUpperCase() + property.type.slice(1)}</span>
                          <span>Price: {formatPrice(property.price, property.type)}</span>
                          <span>Listed: {new Date(property.createdAt).toLocaleDateString()}</span>
                        </div>

                        <div className="flex gap-4 text-sm">
                          <span className="flex items-center gap-1">
                            <Eye className="w-4 h-4" />
                            {property.views} views
                          </span>
                          <span className="text-primary font-medium">
                            {property.leads} leads
                          </span>
                        </div>

                        {property.status === 'rejected' && property.rejectionReason && (
                          <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-md">
                            <p className="text-sm text-red-800">
                              <strong>Rejection Reason:</strong> {property.rejectionReason}
                            </p>
                          </div>
                        )}
                      </div>

                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <Edit className="w-4 h-4 mr-2" />
                          Edit
                        </Button>
                        {property.status === 'approved' && (
                          <Link to={`/property/${property.id}`}>
                            <Button variant="outline" size="sm">
                              <Eye className="w-4 h-4 mr-2" />
                              View
                            </Button>
                          </Link>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}

              {userProperties.length === 0 && (
                <Card>
                  <CardContent className="p-12 text-center">
                    <div className="text-gray-400 mb-4">
                      <Plus className="w-16 h-16 mx-auto" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">
                      No Properties Listed
                    </h3>
                    <p className="text-gray-600 mb-6">
                      Start by listing your first property to reach potential buyers and renters.
                    </p>
                    <Link to="/list-property">
                      <Button>
                        <Plus className="w-4 h-4 mr-2" />
                        List Your First Property
                      </Button>
                    </Link>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>

          {/* Leads Tab */}
          <TabsContent value="leads" className="mt-6">
            <div className="space-y-6">
              {leads.map((lead) => (
                <Card key={lead.id}>
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-lg">{lead.userName}</CardTitle>
                        <CardDescription>{lead.propertyTitle}</CardDescription>
                      </div>
                      <Badge variant="outline">
                        {new Date(lead.createdAt).toLocaleDateString()}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-sm">Phone:</span>
                        <span className="text-sm">{lead.phone}</span>
                      </div>
                      {lead.message && (
                        <div>
                          <span className="font-medium text-sm">Message:</span>
                          <p className="text-sm text-gray-600 mt-1">{lead.message}</p>
                        </div>
                      )}
                      <div className="flex gap-2 pt-2">
                        <Button size="sm">
                          Contact Lead
                        </Button>
                        <Button size="sm" variant="outline">
                          Mark as Contacted
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}

              {leads.length === 0 && (
                <Card>
                  <CardContent className="p-12 text-center">
                    <div className="text-gray-400 mb-4">
                      <User className="w-16 h-16 mx-auto" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">
                      No Leads Yet
                    </h3>
                    <p className="text-gray-600">
                      Once people express interest in your properties, their details will appear here.
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Dashboard;
