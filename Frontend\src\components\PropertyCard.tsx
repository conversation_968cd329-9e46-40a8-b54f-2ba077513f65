
import React from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { MapPin, Home } from 'lucide-react';

interface Property {
  id: string;
  title: string;
  type: string;
  price: number;
  area: number;
  location: string;
  image: string;
  description: string;
}

interface PropertyCardProps {
  property: Property;
}

const PropertyCard = ({ property }: PropertyCardProps) => {
  const formatPrice = (price: number, type: string) => {
    if (type === 'rental' || type === 'shop') {
      return `$${price.toLocaleString()}/month`;
    }
    return `$${price.toLocaleString()}`;
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'rental':
        return 'bg-green-100 text-green-800';
      case 'buying':
        return 'bg-blue-100 text-blue-800';
      case 'plot':
        return 'bg-orange-100 text-orange-800';
      case 'shop':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Link to={`/property/${property.id}`}>
      <Card className="property-card overflow-hidden h-full">
        <div className="relative">
          <img
            src={property.image}
            alt={property.title}
            className="w-full h-48 object-cover"
          />
          <Badge className={`absolute top-3 left-3 ${getTypeColor(property.type)}`}>
            {property.type.charAt(0).toUpperCase() + property.type.slice(1)}
          </Badge>
        </div>
        <CardContent className="p-4">
          <h3 className="font-semibold text-lg mb-2 line-clamp-2">
            {property.title}
          </h3>
          <p className="text-2xl font-bold text-primary mb-2">
            {formatPrice(property.price, property.type)}
          </p>
          <div className="flex items-center text-gray-600 mb-2">
            <Home className="w-4 h-4 mr-1" />
            <span className="text-sm">{property.area} sq ft</span>
          </div>
          <div className="flex items-center text-gray-600 mb-3">
            <MapPin className="w-4 h-4 mr-1" />
            <span className="text-sm">{property.location}</span>
          </div>
          <p className="text-gray-600 text-sm line-clamp-2">
            {property.description}
          </p>
        </CardContent>
      </Card>
    </Link>
  );
};

export default PropertyCard;
