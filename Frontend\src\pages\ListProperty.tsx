
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import RentalPropertyForm from '@/components/forms/RentalPropertyForm';
import SalePropertyForm from '@/components/forms/SalePropertyForm';
import PlotPropertyForm from '@/components/forms/PlotPropertyForm';
import ShopPropertyForm from '@/components/forms/ShopPropertyForm';

const ListProperty = () => {
  const { toast } = useToast();
  const [propertyPurpose, setPropertyPurpose] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleFormSubmit = async (formData) => {
    setIsSubmitting(true);

    // Validation
    if (!formData.city || !formData.area) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive"
      });
      setIsSubmitting(false);
      return;
    }

    if (!formData.images || formData.images.length === 0) {
      toast({
        title: "Images Required",
        description: "Please upload at least one image of your property.",
        variant: "destructive"
      });
      setIsSubmitting(false);
      return;
    }

    try {
      console.log('Property submission:', formData);
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast({
        title: "Property Listed Successfully!",
        description: "Your property is now under review. You'll be notified once it's approved.",
      });

      // Reset form
      setPropertyPurpose('');
    } catch (error) {
      toast({
        title: "Submission Failed",
        description: "There was an error submitting your property. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderPropertyForm = () => {
    switch (propertyPurpose) {
      case 'rental':
        return <RentalPropertyForm onSubmit={handleFormSubmit} isSubmitting={isSubmitting} />;
      case 'buying':
        return <SalePropertyForm onSubmit={handleFormSubmit} isSubmitting={isSubmitting} />;
      case 'plot':
        return <PlotPropertyForm onSubmit={handleFormSubmit} isSubmitting={isSubmitting} />;
      case 'shop':
        return <ShopPropertyForm onSubmit={handleFormSubmit} isSubmitting={isSubmitting} />;
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              List Your Property
            </h1>
            <p className="text-xl text-gray-600">
              Reach thousands of potential buyers and renters
            </p>
          </div>

          {/* Property Purpose Selection */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Property Purpose</CardTitle>
              <CardDescription>
                First, select what you want to do with your property
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div>
                <Label htmlFor="propertyPurpose">What is your property purpose? *</Label>
                <Select value={propertyPurpose} onValueChange={setPropertyPurpose}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select property purpose" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="rental">For Rental Property</SelectItem>
                    <SelectItem value="buying">For Sale Property</SelectItem>
                    <SelectItem value="plot">For Plots</SelectItem>
                    <SelectItem value="shop">For Shop/Commercial</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Dynamic Form Based on Selection */}
          {propertyPurpose && (
            <div className="space-y-6">
              {renderPropertyForm()}
            </div>
          )}

          {/* Info Card */}
          {propertyPurpose && (
            <Card className="mt-6">
              <CardContent className="p-6">
                <h3 className="font-semibold text-lg mb-3">What happens next?</h3>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                    Your property will be reviewed by our team within 24-48 hours
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                    Once approved, it will be visible to all users on the platform
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                    You'll receive notifications when users express interest in your property
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                    Track views and manage leads from your dashboard
                  </li>
                </ul>
              </CardContent>
            </Card>
          )}

          {/* Supabase Note */}
          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <p className="text-sm text-blue-800">
              <strong>Note:</strong> To enable full functionality including file uploads and database storage, 
              please connect this project to Supabase using the green button in the top right.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ListProperty;
