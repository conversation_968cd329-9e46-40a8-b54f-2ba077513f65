
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import RentRequirementForm from '@/components/forms/RentRequirementForm';
import SaleRequirementForm from '@/components/forms/SaleRequirementForm';

const RequirementForm = () => {
  const { toast } = useToast();
  const [formType, setFormType] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleFormSubmit = async (formData) => {
    setIsSubmitting(true);

    try {
      console.log('Requirement submission:', formData);
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast({
        title: "Requirement Submitted Successfully!",
        description: "We've received your requirement. Our team will contact you shortly with matching properties.",
      });

      // Reset form
      setFormType('');
    } catch (error) {
      toast({
        title: "Submission Failed",
        description: "There was an error submitting your requirement. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderRequirementForm = () => {
    switch (formType) {
      case 'rent':
        return <RentRequirementForm onSubmit={handleFormSubmit} isSubmitting={isSubmitting} />;
      case 'sale':
        return <SaleRequirementForm onSubmit={handleFormSubmit} isSubmitting={isSubmitting} />;
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Submit Your Property Requirement
            </h1>
            <p className="text-xl text-gray-600">
              Tell us what you're looking for and we'll find the perfect match
            </p>
          </div>

          {/* Form Type Selection */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>What are you looking for?</CardTitle>
              <CardDescription>
                Choose the type of property requirement you want to submit
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div>
                <Label htmlFor="formType">Requirement Type *</Label>
                <Select value={formType} onValueChange={setFormType}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select requirement type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="rent">Property for Rent</SelectItem>
                    <SelectItem value="sale">Property for Purchase</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Dynamic Form Based on Selection */}
          {formType && (
            <div className="space-y-6">
              {renderRequirementForm()}
            </div>
          )}

          {/* Info Card */}
          {formType && (
            <Card className="mt-6">
              <CardContent className="p-6">
                <h3 className="font-semibold text-lg mb-3">What happens next?</h3>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                    Our team will review your requirement within 2-4 hours
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                    We'll match your requirements with verified properties in our database
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                    You'll receive personalized property recommendations via call/WhatsApp
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                    Our experts will assist you throughout the entire process
                  </li>
                </ul>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default RequirementForm;
