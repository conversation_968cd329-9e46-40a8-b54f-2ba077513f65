
import { initializeApp } from "firebase/app";
import { getAuth, GoogleAuthProvider } from "firebase/auth";
import { getFirestore } from "firebase/firestore";
import { getStorage } from "firebase/storage";

const firebaseConfig = {
  apiKey: "AIzaSyAH4eKuCQrQNM6fW6VWxw1ZA8--qcSA2xA",
  authDomain: "homodeal-df63b.firebaseapp.com",
  projectId: "homodeal-df63b",
  storageBucket: "homodeal-df63b.firebasestorage.app",
  messagingSenderId: "238923307944",
  appId: "1:238923307944:web:f7e30cda95f38405a79b9d",
  measurementId: "G-33FHVE0Q6F"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Authentication and get a reference to the service
export const auth = getAuth(app);
export const googleProvider = new GoogleAuthProvider();

// Initialize Cloud Firestore and get a reference to the service
export const db = getFirestore(app);

// Initialize Cloud Storage and get a reference to the service
export const storage = getStorage(app);

export default app;
