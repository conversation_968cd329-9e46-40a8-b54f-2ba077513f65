
import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

const SaleRequirementForm = ({ onSubmit, isSubmitting }) => {
  const [formData, setFormData] = useState({
    // Basic Information
    fullName: '',
    contactNumber: '',
    email: '',
    
    // Requirement Type
    requirementType: '',
    
    // Residential Properties
    propertyTypeResidential: '',
    bhkConfiguration: [],
    washrooms: [],
    builtUpAreaMin: '',
    builtUpAreaMax: '',
    furnishingPreference: '',
    propertyAge: '',
    
    // Commercial Properties
    propertyTypeCommercial: '',
    commercialAreaMin: '',
    commercialAreaMax: '',
    floorPreference: '',
    parkingNeeded: '',
    
    // Location & Budget
    preferredLocations: [],
    budgetMin: '',
    budgetMax: '',
    
    // Timeline
    purchaseTimeline: '',
    
    // Additional
    additionalRequirements: ''
  });

  const handleInputChange = (e) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  const handleSelectChange = (name, value) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleCheckboxChange = (name, value, checked) => {
    setFormData(prev => ({
      ...prev,
      [name]: checked 
        ? [...prev[name], value]
        : prev[name].filter(item => item !== value)
    }));
  };

  const handleLocationChange = (location, checked) => {
    setFormData(prev => ({
      ...prev,
      preferredLocations: checked 
        ? [...prev.preferredLocations, location]
        : prev.preferredLocations.filter(loc => loc !== location)
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit({ ...formData, formType: 'sale-requirement' });
  };

  const locations = [
    'Central Delhi', 'South Delhi', 'North Delhi', 'East Delhi', 'West Delhi',
    'Gurgaon', 'Noida', 'Faridabad', 'Ghaziabad', 'Greater Noida'
  ];

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle>Basic Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="fullName">Full Name *</Label>
            <Input
              id="fullName"
              name="fullName"
              value={formData.fullName}
              onChange={handleInputChange}
              required
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="contactNumber">Contact Number *</Label>
              <Input
                id="contactNumber"
                name="contactNumber"
                type="tel"
                value={formData.contactNumber}
                onChange={handleInputChange}
                required
              />
            </div>
            <div>
              <Label htmlFor="email">Email (Optional)</Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Requirement Type */}
      <Card>
        <CardHeader>
          <CardTitle>Requirement Type</CardTitle>
        </CardHeader>
        <CardContent>
          <RadioGroup 
            value={formData.requirementType} 
            onValueChange={(value) => handleSelectChange('requirementType', value)}
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="residential" id="residential" />
              <Label htmlFor="residential">Residential</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="commercial" id="commercial" />
              <Label htmlFor="commercial">Commercial</Label>
            </div>
          </RadioGroup>
        </CardContent>
      </Card>

      {/* Residential Properties */}
      {formData.requirementType === 'residential' && (
        <Card>
          <CardHeader>
            <CardTitle>Residential Property Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label>Property Type (Residential) *</Label>
              <Select value={formData.propertyTypeResidential} onValueChange={(value) => handleSelectChange('propertyTypeResidential', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select property type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="flat">Flat</SelectItem>
                  <SelectItem value="independent-house">Independent House</SelectItem>
                  <SelectItem value="builder-floor">Builder Floor</SelectItem>
                  <SelectItem value="villa">Villa</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>BHK Configuration (Multiple Selection)</Label>
              <div className="grid grid-cols-3 md:grid-cols-5 gap-3 mt-2">
                {['1 BHK', '2 BHK', '3 BHK', '4 BHK', '5+ BHK'].map((bhk) => (
                  <div key={bhk} className="flex items-center space-x-2">
                    <Checkbox
                      id={bhk}
                      checked={formData.bhkConfiguration.includes(bhk)}
                      onCheckedChange={(checked) => handleCheckboxChange('bhkConfiguration', bhk, checked)}
                    />
                    <Label htmlFor={bhk} className="text-sm">{bhk}</Label>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <Label>No. of Washrooms (Multiple Selection)</Label>
              <div className="grid grid-cols-4 gap-3 mt-2">
                {['1', '2', '3', '4+'].map((washroom) => (
                  <div key={washroom} className="flex items-center space-x-2">
                    <Checkbox
                      id={`washroom-${washroom}`}
                      checked={formData.washrooms.includes(washroom)}
                      onCheckedChange={(checked) => handleCheckboxChange('washrooms', washroom, checked)}
                    />
                    <Label htmlFor={`washroom-${washroom}`} className="text-sm">{washroom}</Label>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <Label>Built-up Area (sq. ft.)</Label>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="builtUpAreaMin">Min</Label>
                  <Input
                    id="builtUpAreaMin"
                    name="builtUpAreaMin"
                    type="number"
                    value={formData.builtUpAreaMin}
                    onChange={handleInputChange}
                    placeholder="Min area"
                  />
                </div>
                <div>
                  <Label htmlFor="builtUpAreaMax">Max</Label>
                  <Input
                    id="builtUpAreaMax"
                    name="builtUpAreaMax"
                    type="number"
                    value={formData.builtUpAreaMax}
                    onChange={handleInputChange}
                    placeholder="Max area"
                  />
                </div>
              </div>
            </div>

            <div>
              <Label>Furnishing Preference</Label>
              <RadioGroup 
                value={formData.furnishingPreference} 
                onValueChange={(value) => handleSelectChange('furnishingPreference', value)}
              >
                {['Unfurnished', 'Semi-Furnished', 'Fully Furnished', "Doesn't Matter"].map((option) => (
                  <div key={option} className="flex items-center space-x-2">
                    <RadioGroupItem value={option.toLowerCase().replace(/[^a-z0-9]/g, '-')} id={`furnishing-${option}`} />
                    <Label htmlFor={`furnishing-${option}`}>{option}</Label>
                  </div>
                ))}
              </RadioGroup>
            </div>

            <div>
              <Label>Age of Property</Label>
              <RadioGroup 
                value={formData.propertyAge} 
                onValueChange={(value) => handleSelectChange('propertyAge', value)}
              >
                {['New', '< 5 yrs', '5–10 yrs', '10+ yrs', "Doesn't Matter"].map((option) => (
                  <div key={option} className="flex items-center space-x-2">
                    <RadioGroupItem value={option.toLowerCase().replace(/[^a-z0-9]/g, '-')} id={`age-${option}`} />
                    <Label htmlFor={`age-${option}`}>{option}</Label>
                  </div>
                ))}
              </RadioGroup>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Commercial Properties */}
      {formData.requirementType === 'commercial' && (
        <Card>
          <CardHeader>
            <CardTitle>Commercial Property Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label>Property Type (Commercial) *</Label>
              <Select value={formData.propertyTypeCommercial} onValueChange={(value) => handleSelectChange('propertyTypeCommercial', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select commercial property type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="shop">Shop</SelectItem>
                  <SelectItem value="office">Office</SelectItem>
                  <SelectItem value="showroom">Showroom</SelectItem>
                  <SelectItem value="warehouse">Warehouse</SelectItem>
                  <SelectItem value="commercial-plot">Commercial Plot</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Built-up Area Requirement</Label>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="commercialAreaMin">Min</Label>
                  <Input
                    id="commercialAreaMin"
                    name="commercialAreaMin"
                    type="number"
                    value={formData.commercialAreaMin}
                    onChange={handleInputChange}
                    placeholder="Min area"
                  />
                </div>
                <div>
                  <Label htmlFor="commercialAreaMax">Max</Label>
                  <Input
                    id="commercialAreaMax"
                    name="commercialAreaMax"
                    type="number"
                    value={formData.commercialAreaMax}
                    onChange={handleInputChange}
                    placeholder="Max area"
                  />
                </div>
              </div>
            </div>

            <div>
              <Label>Floor Preference</Label>
              <RadioGroup 
                value={formData.floorPreference} 
                onValueChange={(value) => handleSelectChange('floorPreference', value)}
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="ground-floor-must" id="ground-floor-must" />
                  <Label htmlFor="ground-floor-must">Ground Floor Must</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="upper-floor-ok" id="upper-floor-ok" />
                  <Label htmlFor="upper-floor-ok">Upper Floor OK</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="basement-ok" id="basement-ok" />
                  <Label htmlFor="basement-ok">Basement OK</Label>
                </div>
              </RadioGroup>
            </div>

            <div>
              <Label>Parking Needed?</Label>
              <RadioGroup 
                value={formData.parkingNeeded} 
                onValueChange={(value) => handleSelectChange('parkingNeeded', value)}
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="yes" id="parking-yes" />
                  <Label htmlFor="parking-yes">Yes</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="no" id="parking-no" />
                  <Label htmlFor="parking-no">No</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="doesnt-matter" id="parking-doesnt-matter" />
                  <Label htmlFor="parking-doesnt-matter">Doesn't Matter</Label>
                </div>
              </RadioGroup>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Location & Budget */}
      {formData.requirementType && (
        <Card>
          <CardHeader>
            <CardTitle>Location & Budget</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label>Preferred Location(s) (Multiple Selection)</Label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3 mt-2">
                {locations.map((location) => (
                  <div key={location} className="flex items-center space-x-2">
                    <Checkbox
                      id={`sale-${location}`}
                      checked={formData.preferredLocations.includes(location)}
                      onCheckedChange={(checked) => handleLocationChange(location, checked)}
                    />
                    <Label htmlFor={`sale-${location}`} className="text-sm">{location}</Label>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <Label>Budget (₹)</Label>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="budgetMin">Min</Label>
                  <Input
                    id="budgetMin"
                    name="budgetMin"
                    type="number"
                    value={formData.budgetMin}
                    onChange={handleInputChange}
                    placeholder="Min budget"
                  />
                </div>
                <div>
                  <Label htmlFor="budgetMax">Max</Label>
                  <Input
                    id="budgetMax"
                    name="budgetMax"
                    type="number"
                    value={formData.budgetMax}
                    onChange={handleInputChange}
                    placeholder="Max budget"
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Purchase Timeline */}
      {formData.requirementType && (
        <Card>
          <CardHeader>
            <CardTitle>Purchase Timeline</CardTitle>
          </CardHeader>
          <CardContent>
            <RadioGroup 
              value={formData.purchaseTimeline} 
              onValueChange={(value) => handleSelectChange('purchaseTimeline', value)}
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="immediately" id="immediately" />
                <Label htmlFor="immediately">Immediately</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="within-1-month" id="within-1-month" />
                <Label htmlFor="within-1-month">Within 1 month</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="within-3-months" id="within-3-months" />
                <Label htmlFor="within-3-months">Within 3 months</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="just-exploring" id="just-exploring" />
                <Label htmlFor="just-exploring">Just Exploring</Label>
              </div>
            </RadioGroup>
          </CardContent>
        </Card>
      )}

      {/* Additional Requirements */}
      {formData.requirementType && (
        <Card>
          <CardHeader>
            <CardTitle>Additional Requirements</CardTitle>
            <CardDescription>Any specific requirements or preferences (Optional)</CardDescription>
          </CardHeader>
          <CardContent>
            <Textarea
              name="additionalRequirements"
              value={formData.additionalRequirements}
              onChange={handleInputChange}
              rows={4}
              placeholder="Tell us about any additional requirements, preferences, or special needs..."
            />
          </CardContent>
        </Card>
      )}

      {/* Submit Button */}
      {formData.requirementType && (
        <div className="pt-6">
          <Button 
            type="submit" 
            className="w-full bg-blue-600 hover:bg-blue-700" 
            size="lg"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Submitting...' : 'Submit Purchase Requirement'}
          </Button>
        </div>
      )}
    </form>
  );
};

export default SaleRequirementForm;
