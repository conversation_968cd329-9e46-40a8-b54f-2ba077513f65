
import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

const RentRequirementForm = ({ onSubmit, isSubmitting }) => {
  const [formData, setFormData] = useState({
    name: '',
    category: '',
    gender: '',
    contactNumber: '',
    email: '',
    propertyType: '',
    furnishingType: '',
    preferredLocations: [],
    numberOfPersons: '',
    budget: '',
    additional: ''
  });

  const handleInputChange = (e) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  const handleSelectChange = (name, value) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleLocationChange = (location, checked) => {
    setFormData(prev => ({
      ...prev,
      preferredLocations: checked 
        ? [...prev.preferredLocations, location]
        : prev.preferredLocations.filter(loc => loc !== location)
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit({ ...formData, formType: 'rent-requirement' });
  };

  const locations = [
    'Central Delhi', 'South Delhi', 'North Delhi', 'East Delhi', 'West Delhi',
    'Gurgaon', 'Noida', 'Faridabad', 'Ghaziabad', 'Greater Noida'
  ];

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle>Basic Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="name">Full Name *</Label>
            <Input
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              required
            />
          </div>

          <div>
            <Label>Your Category *</Label>
            <RadioGroup 
              value={formData.category} 
              onValueChange={(value) => handleSelectChange('category', value)}
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="student" id="student" />
                <Label htmlFor="student">Student</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="working-professional" id="working-professional" />
                <Label htmlFor="working-professional">Working Professional</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="family" id="family" />
                <Label htmlFor="family">Family</Label>
              </div>
            </RadioGroup>
          </div>

          {(formData.category === 'student' || formData.category === 'working-professional') && (
            <div>
              <Label>Gender *</Label>
              <RadioGroup 
                value={formData.gender} 
                onValueChange={(value) => handleSelectChange('gender', value)}
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="male" id="male" />
                  <Label htmlFor="male">Male</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="female" id="female" />
                  <Label htmlFor="female">Female</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="other" id="other" />
                  <Label htmlFor="other">Other</Label>
                </div>
              </RadioGroup>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="contactNumber">Contact Number *</Label>
              <Input
                id="contactNumber"
                name="contactNumber"
                type="tel"
                value={formData.contactNumber}
                onChange={handleInputChange}
                required
              />
            </div>
            <div>
              <Label htmlFor="email">Email (Optional)</Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Property Requirements */}
      <Card>
        <CardHeader>
          <CardTitle>Property Requirements</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label>Property Type *</Label>
            <Select value={formData.propertyType} onValueChange={(value) => handleSelectChange('propertyType', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select property type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="flat">Flat</SelectItem>
                <SelectItem value="room">Room</SelectItem>
                <SelectItem value="independent-house">Independent House</SelectItem>
                <SelectItem value="builder-floor">Builder Floor</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label>Furnishing Type *</Label>
            <Select value={formData.furnishingType} onValueChange={(value) => handleSelectChange('furnishingType', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select furnishing type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="furnished">Furnished</SelectItem>
                <SelectItem value="semi-furnished">Semi-Furnished</SelectItem>
                <SelectItem value="unfurnished">Unfurnished</SelectItem>
                <SelectItem value="doesnt-matter">Doesn't Matter</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label>Preferred Locations (Multiple Selection)</Label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3 mt-2">
              {locations.map((location) => (
                <div key={location} className="flex items-center space-x-2">
                  <Checkbox
                    id={location}
                    checked={formData.preferredLocations.includes(location)}
                    onCheckedChange={(checked) => handleLocationChange(location, checked)}
                  />
                  <Label htmlFor={location} className="text-sm">{location}</Label>
                </div>
              ))}
            </div>
          </div>

          {formData.category !== 'family' && (
            <div>
              <Label htmlFor="numberOfPersons">Number of Persons *</Label>
              <Input
                id="numberOfPersons"
                name="numberOfPersons"
                type="number"
                value={formData.numberOfPersons}
                onChange={handleInputChange}
                placeholder="How many people will stay?"
                required
              />
            </div>
          )}

          <div>
            <Label htmlFor="budget">Budget (₹ per month) *</Label>
            <Input
              id="budget"
              name="budget"
              type="number"
              value={formData.budget}
              onChange={handleInputChange}
              placeholder="Enter your budget"
              required
            />
          </div>
        </CardContent>
      </Card>

      {/* Additional Requirements */}
      <Card>
        <CardHeader>
          <CardTitle>Additional Requirements</CardTitle>
          <CardDescription>Any specific requirements or preferences</CardDescription>
        </CardHeader>
        <CardContent>
          <Textarea
            name="additional"
            value={formData.additional}
            onChange={handleInputChange}
            rows={4}
            placeholder="Tell us about any additional requirements, preferences, or special needs..."
          />
        </CardContent>
      </Card>

      {/* Submit Button */}
      <div className="pt-6">
        <Button 
          type="submit" 
          className="w-full bg-blue-600 hover:bg-blue-700" 
          size="lg"
          disabled={isSubmitting}
        >
          {isSubmitting ? 'Submitting...' : 'Submit Rent Requirement'}
        </Button>
      </div>
    </form>
  );
};

export default RentRequirementForm;
