import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Upload, X } from 'lucide-react';

const SalePropertyForm = ({ onSubmit, isSubmitting }) => {
  const [formData, setFormData] = useState({
    // Property Type
    propertyType: '',
    
    // Configuration
    bhkType: '',
    washrooms: '',
    
    // Property Size
    builtUpArea: '',
    
    // Floor Details
    currentFloor: '',
    totalFloors: '',
    liftAvailable: '',
    
    // Furnishing
    furnishingStatus: '',
    
    // Age of Property
    propertyAge: '',
    
    // Location (same as rental)
    houseNumber: '',
    streetNumber: '',
    streetName: '',
    area: '',
    landmark: '',
    city: '',
    pinCode: '',
    
    // Pricing
    sellingPrice: '',
    negotiable: '',
    
    // Description
    description: ''
  });

  const [images, setImages] = useState([]);

  const handleInputChange = (e) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  const handleSelectChange = (name, value) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleImageUpload = (e) => {
    const files = e.target.files;
    if (files) {
      const newImages = Array.from(files).map((file, index) => {
        return `https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-4.0.3&w=800&q=80&sig=${Date.now()}-${index}`;
      });
      setImages(prev => [...prev, ...newImages].slice(0, 10));
    }
  };

  const removeImage = (index) => {
    setImages(prev => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit({ ...formData, images, propertyCategory: 'buying' });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Property Type */}
      <Card>
        <CardHeader>
          <CardTitle>Property Type</CardTitle>
        </CardHeader>
        <CardContent>
          <Select value={formData.propertyType} onValueChange={(value) => handleSelectChange('propertyType', value)}>
            <SelectTrigger>
              <SelectValue placeholder="Select property type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="flat">Flat</SelectItem>
              <SelectItem value="independent-house">Independent House</SelectItem>
              <SelectItem value="builder-floor">Builder Floor</SelectItem>
              <SelectItem value="villa">Villa</SelectItem>
            </SelectContent>
          </Select>
        </CardContent>
      </Card>

      {/* Configuration */}
      <Card>
        <CardHeader>
          <CardTitle>Configuration</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label>Room Configuration</Label>
            <Select value={formData.bhkType} onValueChange={(value) => handleSelectChange('bhkType', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select BHK type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1bhk">1 BHK</SelectItem>
                <SelectItem value="2bhk">2 BHK</SelectItem>
                <SelectItem value="3bhk">3 BHK</SelectItem>
                <SelectItem value="4bhk">4 BHK</SelectItem>
                <SelectItem value="5+bhk">5+ BHK</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <Label>Number of Washrooms</Label>
            <Select value={formData.washrooms} onValueChange={(value) => handleSelectChange('washrooms', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select washrooms" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1">1</SelectItem>
                <SelectItem value="2">2</SelectItem>
                <SelectItem value="3">3</SelectItem>
                <SelectItem value="4+">4+</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Property Size */}
      <Card>
        <CardHeader>
          <CardTitle>Property Size</CardTitle>
        </CardHeader>
        <CardContent>
          <div>
            <Label htmlFor="builtUpArea">Built-up Area (sq. ft.) *</Label>
            <Input
              id="builtUpArea"
              name="builtUpArea"
              type="number"
              value={formData.builtUpArea}
              onChange={handleInputChange}
              placeholder="e.g., 1200"
              required
            />
          </div>
        </CardContent>
      </Card>

      {/* Floor Details */}
      <Card>
        <CardHeader>
          <CardTitle>Floor Details</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {(formData.propertyType === 'flat' || formData.propertyType === 'builder-floor') && (
            <>
              <div>
                <Label htmlFor="currentFloor">Current Floor</Label>
                <Input
                  id="currentFloor"
                  name="currentFloor"
                  value={formData.currentFloor}
                  onChange={handleInputChange}
                  placeholder="e.g., 3rd Floor"
                />
              </div>
              
              <div>
                <Label htmlFor="totalFloors">Total Floors in Building</Label>
                <Input
                  id="totalFloors"
                  name="totalFloors"
                  value={formData.totalFloors}
                  onChange={handleInputChange}
                  placeholder="e.g., 10"
                />
              </div>
              
              <div>
                <Label>Lift Available</Label>
                <RadioGroup 
                  value={formData.liftAvailable} 
                  onValueChange={(value) => handleSelectChange('liftAvailable', value)}
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="yes" id="lift-yes" />
                    <Label htmlFor="lift-yes">Yes</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="no" id="lift-no" />
                    <Label htmlFor="lift-no">No</Label>
                  </div>
                </RadioGroup>
              </div>
            </>
          )}
          
          {formData.propertyType === 'independent-house' && (
            <div>
              <Label htmlFor="totalFloors">Number of Floors</Label>
              <Input
                id="totalFloors"
                name="totalFloors"
                value={formData.totalFloors}
                onChange={handleInputChange}
                placeholder="e.g., 2"
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Furnishing Status */}
      <Card>
        <CardHeader>
          <CardTitle>Furnishing Status</CardTitle>
        </CardHeader>
        <CardContent>
          <RadioGroup 
            value={formData.furnishingStatus} 
            onValueChange={(value) => handleSelectChange('furnishingStatus', value)}
          >
            {['Unfurnished', 'Semi-Furnished', 'Fully Furnished'].map((option) => (
              <div key={option} className="flex items-center space-x-2">
                <RadioGroupItem value={option.toLowerCase().replace(' ', '-')} id={option} />
                <Label htmlFor={option}>{option}</Label>
              </div>
            ))}
          </RadioGroup>
        </CardContent>
      </Card>

      {/* Age of Property */}
      <Card>
        <CardHeader>
          <CardTitle>Age of Property</CardTitle>
        </CardHeader>
        <CardContent>
          <RadioGroup 
            value={formData.propertyAge} 
            onValueChange={(value) => handleSelectChange('propertyAge', value)}
          >
            {['New', 'Less than 5 years', '5-10 years', 'More than 10 years'].map((option) => (
              <div key={option} className="flex items-center space-x-2">
                <RadioGroupItem value={option.toLowerCase().replace(/\s+/g, '-')} id={option} />
                <Label htmlFor={option}>{option}</Label>
              </div>
            ))}
          </RadioGroup>
        </CardContent>
      </Card>

      {/* Location Details - Same as Rental */}
      <Card>
        <CardHeader>
          <CardTitle>Location Details</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="houseNumber">House Number *</Label>
              <Input
                id="houseNumber"
                name="houseNumber"
                value={formData.houseNumber}
                onChange={handleInputChange}
                required
              />
            </div>
            <div>
              <Label htmlFor="streetNumber">Street Number</Label>
              <Input
                id="streetNumber"
                name="streetNumber"
                value={formData.streetNumber}
                onChange={handleInputChange}
              />
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="streetName">Street Name *</Label>
              <Input
                id="streetName"
                name="streetName"
                value={formData.streetName}
                onChange={handleInputChange}
                required
              />
            </div>
            <div>
              <Label htmlFor="area">Area *</Label>
              <Input
                id="area"
                name="area"
                value={formData.area}
                onChange={handleInputChange}
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="landmark">Landmark</Label>
              <Input
                id="landmark"
                name="landmark"
                value={formData.landmark}
                onChange={handleInputChange}
              />
            </div>
            <div>
              <Label htmlFor="city">City *</Label>
              <Input
                id="city"
                name="city"
                value={formData.city}
                onChange={handleInputChange}
                required
              />
            </div>
            <div>
              <Label htmlFor="pinCode">Pin Code *</Label>
              <Input
                id="pinCode"
                name="pinCode"
                value={formData.pinCode}
                onChange={handleInputChange}
                required
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Pricing Details */}
      <Card>
        <CardHeader>
          <CardTitle>Pricing Details</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="sellingPrice">Expected Selling Price (₹) *</Label>
            <Input
              id="sellingPrice"
              name="sellingPrice"
              type="number"
              value={formData.sellingPrice}
              onChange={handleInputChange}
              placeholder="Enter expected price"
              required
            />
          </div>
          
          <div>
            <Label>Negotiable?</Label>
            <RadioGroup 
              value={formData.negotiable} 
              onValueChange={(value) => handleSelectChange('negotiable', value)}
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="yes" id="negotiable-yes" />
                <Label htmlFor="negotiable-yes">Yes</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="no" id="negotiable-no" />
                <Label htmlFor="negotiable-no">No</Label>
              </div>
            </RadioGroup>
          </div>
        </CardContent>
      </Card>

      {/* Photos Upload */}
      <Card>
        <CardHeader>
          <CardTitle>Property Photos</CardTitle>
          <CardDescription>Upload 5-10 high-quality images</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            <input
              type="file"
              multiple
              accept="image/*"
              onChange={handleImageUpload}
              className="hidden"
              id="sale-image-upload"
            />
            <label
              htmlFor="sale-image-upload"
              className="cursor-pointer flex flex-col items-center"
            >
              <Upload className="w-12 h-12 text-gray-400 mb-4" />
              <p className="text-lg font-medium text-gray-900 mb-2">
                Upload Property Images
              </p>
              <p className="text-sm text-gray-600">
                Choose multiple images (JPG, PNG, WebP)
              </p>
            </label>
          </div>

          {images.length > 0 && (
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              {images.map((image, index) => (
                <div key={index} className="relative group">
                  <img
                    src={image}
                    alt={`Property ${index + 1}`}
                    className="w-full h-32 object-cover rounded-lg"
                  />
                  <button
                    type="button"
                    onClick={() => removeImage(index)}
                    className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Description */}
      <Card>
        <CardHeader>
          <CardTitle>Description</CardTitle>
        </CardHeader>
        <CardContent>
          <Textarea
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            rows={4}
            placeholder="Describe your property, its features, amenities, and what makes it special..."
          />
        </CardContent>
      </Card>

      {/* Submit Button */}
      <div className="pt-6">
        <Button 
          type="submit" 
          className="w-full bg-blue-600 hover:bg-blue-700" 
          size="lg"
          disabled={isSubmitting}
        >
          {isSubmitting ? 'Submitting...' : 'Submit Property for Sale'}
        </Button>
      </div>
    </form>
  );
};

export default SalePropertyForm;
