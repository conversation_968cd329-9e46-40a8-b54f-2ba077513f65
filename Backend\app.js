// server.js
import express from "express";
import verifyToken from "./middlewares/verifyToken.js";

const app = express();

app.use(express.json());

// Public route
app.get("/", (req, res) => {
  res.send("Public endpoint");
});

// Protected route
app.get("/profile", verifyToken, (req, res) => {
  res.json({
    message: "Access granted",
    user: req.user, // comes from decoded Firebase token
  });
});

app.listen(5000, () => console.log("Server running on port 5000"));
